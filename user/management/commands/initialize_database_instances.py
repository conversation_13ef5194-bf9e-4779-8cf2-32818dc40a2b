import os
import json
import uuid
from typing import Dict, Any, List, Optional
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from datetime import datetime
from django.utils import timezone

# Import all required models
from setting.services import SchedulingService
from setting.models import MessageTemplate, SystemSettings, SLA
from user.models import (
    User, Role, UserRole, Department, UserSchedule, UserTag, 
    Permission, RolePermission, UserPlatformIdentity
)
from customer.models import (
    Gender, Interface, CustomerTag, Customer, CustomerPlatformIdentity
)
from ticket.models import Status, TicketPriority, TicketTopic
from llm_rag_doc.models import Company
from connectors.models import LineChannel, WhatsAppChannel, FacebookChannel
from consent.models import AgreementType, AgreementVersion, ConsentPurpose

from linebot.v3.messaging import (
    Configuration,
    ApiClient,
    MessagingApi,
    MessagingApiBlob,
    TextMessage,
    TextMessageV2,
    ImageMessage,
    ImagemapMessage,
    ImagemapAction,
    ImagemapBaseSize,
    MessageImagemapAction,
    PostbackAction,
    PushMessageRequest,
    ReplyMessageRequest,
    QuickReply,
    QuickReplyItem,
)
from linebot.v3.messaging.models import (
    SubstitutionObject,
    MentionSubstitutionObject,
    MentionTarget,
    AllMentionTarget,
    UserMentionTarget
)

from setting.utils import refresh_image_sas_tokens

class Command(BaseCommand):
    help = 'Initialize pre-defined instances in database with support for multi-platform identification'

    def handle(self, *args, **kwargs):
        try:
            with transaction.atomic():
                # Phase 1: Create core system users
                admin_user, system_user = self._create_system_users()
                
                # Phase 2: Create organizational structure
                self._create_departments(system_user)
                self._create_companies(system_user)
                
                # Phase 3: Create roles and permissions
                self._create_roles_and_permissions(system_user)
                self._assign_user_roles(admin_user, system_user)
                
                # Phase 4: Create customer-related entities
                self._create_genders(system_user)
                self._create_interfaces(system_user)
                self._create_customer_tags(system_user)
                
                # Phase 5: Create ticket-related entities
                self._create_ticket_statuses(system_user)
                self._create_ticket_priorities(system_user)
                self._create_ticket_topics(system_user)
                
                # Phase 6: Create user tags
                self._create_user_tags(system_user)
                
                # Phase 7: Create system settings
                self._create_system_settings(system_user)
                
                # Phase 8: Create business hours and schedules
                self._create_business_hours(system_user)
                self._create_system_user_schedule(system_user)
                self._create_default_user_schedules()
                
                # # Phase 9: Create default channels (if environment variables exist)
                # self._create_default_channels(system_user)

                # Phase 10: create MessageTemplates
                self._create_line_csat_score(system_user)

                # Phase 11: Create consent agreements
                self._create_consent_agreements(system_user)
                
                # Phase 12: create SLA instances
                self._create_slas()
                
                self.stdout.write(self.style.SUCCESS('Database initialization completed successfully!'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during initialization: {str(e)}'))
            raise

    def _create_system_users(self):
        """Create admin and system superusers"""
        # Create 'admin' superuser
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'work_email': '<EMAIL>',
                'name': 'Admin',
                'first_name': 'Admin',
                'last_name': 'User',
                'employee_id': '1',
                'universal_id': uuid.uuid4(),
                'job_title': 'System Administrator',
                'position_level': 'EXECUTIVE',
                'employment_type': 'FULL_TIME',
                'access_level': 'SUPER_ADMIN',
                'status': 'offline',
                'preferred_language': 'en',
                'preferred_interface': 'WEB',
            }
        )
        if created:
            admin_user.set_password('adminPW01!')
            admin_user.is_superuser = True
            admin_user.is_staff = True
            admin_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "admin" superuser'))
        else:
            self.stdout.write(self.style.WARNING('"admin" superuser already exists'))

        # Create 'system' superuser
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'work_email': '<EMAIL>',
                'name': 'System',
                'first_name': 'System',
                'last_name': 'Bot',
                'employee_id': '2',
                'universal_id': uuid.uuid4(),
                'job_title': 'System Bot',
                # 'position_level': 'EXECUTIVE',
                # 'employment_type': 'FULL_TIME',
                # 'access_level': 'SUPER_ADMIN',
                'status': 'online',
                # 'preferred_language': 'en',
                # 'preferred_interface': 'WEB',
                'max_concurrent_tickets': 9999,
            }
        )
        if created:
            system_user.set_password('systemPW02!')
            system_user.is_superuser = True
            system_user.is_staff = True
            system_user.save()
            self.stdout.write(self.style.SUCCESS('Successfully created "system" superuser'))
        else:
            self.stdout.write(self.style.WARNING('"system" superuser already exists'))

        return admin_user, system_user

    def _create_departments(self, system_user):
        """Create default departments"""
        departments = [
            {
                'name': 'Marketing',
                'code': 'MKT',
                'description': 'Marketing department'
            },
            {
                'name': 'Customer Service',
                'code': 'CS',
                'description': 'Customer service and support department'
            },
            {
                'name': 'Technical Support',
                'code': 'TS',
                'description': 'Technical support department'
            },
            {
                'name': 'Sales',
                'code': 'SALES',
                'description': 'Sales department'
            },
            {
                'name': 'Administration',
                'code': 'ADMIN',
                'description': 'Administrative department'
            }
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                defaults={
                    'name': dept_data['name'],
                    'description': dept_data['description'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{dept.name}" department'))
            else:
                self.stdout.write(self.style.WARNING(f'"{dept.name}" department already exists'))

    def _create_companies(self, system_user):
        """Create default companies"""
        companies = [
            # {
            #     'name': 'all',
            #     'code': 'all',
            #     'is_default': True
            # },
            {
                'name': 'all',
                'code': 'all',
                'is_default': True
            }
        ]
        
        for company_data in companies:
            company, created = Company.objects.get_or_create(
                code=company_data['code'],
                defaults={
                    'name': company_data['name'],
                    'is_default': company_data['is_default'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{company.name}" company'))
            else:
                self.stdout.write(self.style.WARNING(f'"{company.name}" company already exists'))

    def _create_roles_and_permissions(self, system_user):
        """Create roles and permissions"""
        # Create roles
        # roles = ['Admin', 'System', 'Supervisor', 'Agent', 'Manager', 'Support']
        roles = ['Admin', 'System', 'Supervisor', 'Agent']
        
        for role_name in roles:
            role, created = Role.objects.get_or_create(
                name=role_name,
                defaults={
                    'definition': f'{role_name} role with specific permissions',
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{role_name}" role'))
            else:
                self.stdout.write(self.style.WARNING(f'"{role_name}" role already exists'))
        
        # Create permissions
        permissions = [
            {'name': 'view_all_tickets', 'definition': 'Can view all tickets'},
            {'name': 'assign_tickets', 'definition': 'Can assign tickets to agents'},
            {'name': 'close_tickets', 'definition': 'Can close tickets'},
            {'name': 'manage_users', 'definition': 'Can manage user accounts'},
            {'name': 'view_reports', 'definition': 'Can view system reports'},
            {'name': 'manage_settings', 'definition': 'Can manage system settings'},
        ]
        
        for perm_data in permissions:
            perm, created = Permission.objects.get_or_create(
                name=perm_data['name'],
                defaults={
                    'definition': perm_data['definition'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{perm.name}" permission'))

    def _assign_user_roles(self, admin_user, system_user):
        """Assign roles to system users"""
        admin_role = Role.objects.get(name='Admin')
        system_role = Role.objects.get(name='System')

        UserRole.objects.get_or_create(
            user_id=admin_user,
            role_id=admin_role,
            defaults={'created_by': admin_user}
        )
        
        UserRole.objects.get_or_create(
            user_id=system_user,
            role_id=system_role,
            defaults={'created_by': admin_user}
        )
        
        self.stdout.write(self.style.SUCCESS('Successfully assigned roles to system users'))

    def _create_genders(self, system_user):
        """Create gender options"""
        genders = [
            {'name': 'not_specified', 'definition': 'Not specified'},
            {'name': 'male', 'definition': 'Male Gender'},
            {'name': 'female', 'definition': 'Female Gender'},
            {'name': 'other', 'definition': 'Other Gender'},
        ]
        
        for gender_data in genders:
            gender, created = Gender.objects.get_or_create(
                name=gender_data['name'],
                defaults={
                    'definition': gender_data['definition'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{gender.name}" gender'))
            else:
                self.stdout.write(self.style.WARNING(f'"{gender.name}" gender already exists'))

    def _create_interfaces(self, system_user):
        """Create communication interfaces"""
        interfaces = [
            {'name': 'NONE', 'definition': 'None'},
            {'name': 'CALLING', 'definition': 'Voice Calling'},
            {'name': 'LINE', 'definition': 'LINE Messaging'},
            {'name': 'FBMESSENGER', 'definition': 'Facebook Messenger'},
            {'name': 'WHATSAPP', 'definition': 'WhatsApp Messaging'},
            # {'name': 'TELEGRAM', 'definition': 'Telegram Messaging'},
        ]
        
        for interface_data in interfaces:
            interface, created = Interface.objects.get_or_create(
                name=interface_data['name'],
                defaults={
                    'definition': interface_data['definition'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{interface.name}" interface'))
            else:
                self.stdout.write(self.style.WARNING(f'"{interface.name}" interface already exists'))

    def _create_customer_tags(self, system_user):
        """Create default customer tags"""
        tags = ['VIP', 'New', 'Returning', 'High-Value', 'At-Risk', 'Promotional']
        
        for tag_name in tags:
            tag, created = CustomerTag.objects.get_or_create(
                name=tag_name,
                defaults={'created_by': system_user}
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{tag_name}" customer tag'))

    def _create_user_tags(self, system_user):
        """Create default user tags"""
        tags = [
            'customer_service', 'technical_support', 'sales', 
            'bilingual', 'senior_agent', 'trainer', 'quality_assurance'
        ]
        
        for tag_name in tags:
            tag, created = UserTag.objects.get_or_create(
                name=tag_name,
                defaults={'created_by': system_user}
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{tag_name}" user tag'))

    def _create_ticket_statuses(self, system_user):
        """Create ticket statuses"""
        statuses = [
            {'name': 'default', 'definition': 'Default Status (should not be used)'},
            {'name': 'open', 'definition': 'Open Status - New ticket'},
            {'name': 'assigned', 'definition': 'Assigned Status - Ticket assigned to agent'},
            # {'name': 'in_progress', 'definition': 'In Progress - Being worked on'},
            {'name': 'waiting', 'definition': 'Waiting - Awaiting customer response'},
            {'name': 'pending_to_close', 'definition': 'Pending to Close - Awaiting final confirmation'},
            {'name': 'closed', 'definition': 'Closed Status - Ticket completed'},
            # {'name': 'cancelled', 'definition': 'Cancelled - Ticket cancelled by customer'},
        ]
        
        for status_data in statuses:
            status, created = Status.objects.get_or_create(
                name=status_data['name'],
                defaults={
                    'definition': status_data['definition'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{status.name}" status'))
            else:
                self.stdout.write(self.style.WARNING(f'"{status.name}" status already exists'))

    def _create_ticket_priorities(self, system_user):
        """Create ticket priorities"""
        priorities = [
            {'name': 'Low', 'description': 'Low priority ticket', 'level': 1},
            {'name': 'Medium', 'description': 'Medium priority ticket', 'level': 2},
            {'name': 'High', 'description': 'High priority ticket', 'level': 3},
            {'name': 'Immediately', 'description': 'Immediately priority ticket', 'level': 4},
            # {'name': 'Critical', 'description': 'Critical priority ticket', 'level': 5},
        ]
        
        for priority_data in priorities:
            priority, created = TicketPriority.objects.get_or_create(
                name=priority_data['name'],
                defaults={
                    'description': priority_data['description'],
                    'level': priority_data['level'],
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{priority.name}" priority'))
            else:
                self.stdout.write(self.style.WARNING(f'"{priority.name}" priority already exists'))

    def _create_ticket_topics(self, system_user):
        """Create ticket topics"""
        CASE_TYPES = {
            "Information": [
                "Insurance Products", "Policy Information Inquiry", 
                "Report Insurance Claims", "Application Process",
                "Claiming Compensation", "Check List of Representatives/Agents",
                "Insurance Payment Channels", "Service Network",
                "Contact/Branches", "Follow Up on Previous Reports", "Others"
            ],
            "Reprint": ["Non-motor", "Motor"],
            "Cancel": ["Product", "Agent/Brokers"],
            "Renewal Notice": ["Non-motor", "Motor"],
            "Endorsement": [
                "Name of the Insured", "Name of the Beneficiary",
                "Date of Birth", "Change Address in Policy",
                "National ID Number", "Act", "Others"
            ],
            "Quotation": ["Product"],
            "Complaint": ["Staff", "Product", "Service", "System"],
            "Technical Issue": ["Website", "Mobile App", "API", "Other"],
        }
        
        created_count = 0
        for case_type, case_topics in CASE_TYPES.items():
            for case_topic in case_topics:
                topic, created = TicketTopic.objects.get_or_create(
                    case_type=case_type,
                    case_topic=case_topic,
                    defaults={'created_by': system_user}
                )
                if created:
                    created_count += 1
                    self.stdout.write(self.style.SUCCESS(f'Created topic: {case_type} - {case_topic}'))
        
        self.stdout.write(self.style.NOTICE(f'Created {created_count} new ticket topics'))

    def _create_system_settings(self, system_user):
        """Create system settings"""
        settings_to_create = {
            # Company Settings
            "COMPANY_THAI_NAME": {
                "value": "เอไอเบรนแล็บ",
                "value_type": "text",
                "description": "Company name in Thai",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "COMPANY_ENGLISH_NAME": {
                "value": "AI Brain Lab Insurance Public Company Limited",
                "value_type": "text",
                "description": "Company name in English",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "COMPANY_BUSINESS": {
                "value": "Leading insurance provider offering comprehensive coverage",
                "value_type": "text",
                "description": "Company business description",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "COMPANY_BUSINESS_TYPE": {
                "value": "Insurance",
                "value_type": "text",
                "description": "Type of business",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "COMPANY_MESSAGE_OUTSIDE_BUSINESS_HOURS": {
                "value": "ขออภัย ขณะนี้อยู่นอกเวลาทำการ\n\nSorry, It is currently outside of our business hours.",
                "value_type": "text",
                "description": "Message to inform a customer about outside_business_hours",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "COMPANY_LOGO": {
                "value": "",
                "description": "Company's logo",
                "is_sensitive": False,
                "requires_restart": False,
            },
            
            # Chatbot Settings
            "CHATBOT_MASCOT_THAI_NAME": {
                "value": "น้องซาวเมท",
                "value_type": "text",
                "description": "Chatbot name in Thai",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "CHATBOT_MASCOT_ENGLISH_NAME": {
                "value": "Salmate",
                "value_type": "text",
                "description": "Chatbot name in English",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "CHATBOT_ROLE": {
                "value": "Customer Support",
                "description": "",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "CHATBOT_GENDER": {
                "value": "Female",
                "description": "",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "CHATBOT_CONVERSATION_STYLE": {
                "value": "Formal",
                "description": "",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "CHATBOT_CONVERSATION_TYPE": {
                "value": "Chat",
                "description": "",
                "is_sensitive": False,
                "requires_restart": False,
            },

            # TODO - Probably, in this section, some SystemSettings instances are no longer necessary
            # LINE
            "LINE_OA_QR_CODE": {
                "value": "",
                "description": "LINE Offical account's QR code",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "LINE_OA_QR_LINK": {
                "value": "",
                "description": "LINE ID for customers to add Chatbot as a friend",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "LINE_GROUP_QR_CODE": {
                "value": "",
                "description": "QR code of a company's LINE Group",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "LINE_GROUP_QR_LINK": {
                "value": "",
                "description": "URL for access a company's LINE group",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "LINE_CSAT": {
                "value": "",
                "description": "LINE CSAT for sending at the end of each ticket",
                "is_sensitive": False,
                "requires_restart": False,
            },
            
            # Ticket Management Settings
            "INACTIVE_TICKET_1ST_TIME_MINUTES": {
                "value": os.environ.get("INACTIVE_TICKET_1ST_TIME_MINUTES", "30"),
                "value_type": "text",
                "description": "Minutes before first inactive ticket reminder",
                "is_sensitive": False,
                "requires_restart": True,
            },
            "INACTIVE_TICKET_2ND_TIME_MINUTES": {
                "value": os.environ.get("INACTIVE_TICKET_2ND_TIME_MINUTES", "60"),
                "value_type": "text",
                "description": "Minutes before second inactive ticket reminder",
                "is_sensitive": False,
                "requires_restart": True,
            },
            "INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES": {
                "value": os.environ.get("INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES", "5"),
                "value_type": "text",
                "description": "Minutes to wait for CSAT score before closing",
                "is_sensitive": False,
                "requires_restart": True,
            },
            
            # Auto Transfer Settings
            "AUTO_TRANSFER_TICKET_DEPARTMENT_CONDITION": {
                "value": "false",
                "value_type": "text",
                "description": "Enable department-based auto transfer",
                "is_sensitive": False,
                "requires_restart": True,
            },
            "AUTO_TRANSFER_TICKET_PARTNER_CONDITION": {
                "value": "false",
                "value_type": "text",
                "description": "Enable partner-based auto transfer",
                "is_sensitive": False,
                "requires_restart": True,
            },
            "AUTO_TRANSFER_TICKET_USER_TAG_CONDITION": {
                "value": "false",
                "value_type": "text",
                "description": "Enable user tag-based auto transfer",
                "is_sensitive": False,
                "requires_restart": True,
            },
            "AUTO_TRANSFER_TICKET_SCHEDULE_CONDITION": {
                "value": "false",
                "value_type": "text",
                "description": "Enable user work schedule-based auto transfer",
                "is_sensitive": False,
                "requires_restart": True,
            },
            
            # UI Settings
            "DOMINANT_COLOR": {
                "value": "#3B82F6",
                "value_type": "text",
                "description": "Primary brand color",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "SECONDARY_COLOR": {
                "value": "#84B1F5",
                "value_type": "text",
                "description": "Secondary brand color",
                "is_sensitive": False,
                "requires_restart": False,
            },
            "ACCENT_COLOR": {
                "value": "#FFFFFF",
                "value_type": "text",
                "description": "Accent color",
                "is_sensitive": False,
                "requires_restart": False,
            },
        }
        
        # Add LINE settings if environment variables exist
        if os.environ.get("LINE_CHANNEL_SECRET"):
            settings_to_create.update({
                "LINE_WEBHOOK": {
                    "value": os.environ.get("LINE_WEBHOOK", ""),
                    "description": "URL for connecting chatbot to LINE OA",
                    "is_sensitive": False,
                    "requires_restart": True,
                },
                "LINE_CHANNEL_SECRET": {
                    "value": os.environ.get("LINE_CHANNEL_SECRET", ""),
                    "value_type": "text",
                    "description": "LINE Channel Secret",
                    "is_sensitive": True,
                    "requires_restart": True,
                },
                "LINE_ACCESS_TOKEN": {
                    "value": os.environ.get("LINE_ACCESS_TOKEN", ""),
                    "value_type": "text",
                    "description": "LINE Channel Access Token",
                    "is_sensitive": True,
                    "requires_restart": True,
                },
            })
        
        for key, setting_data in settings_to_create.items():
            setting, created = SystemSettings.objects.get_or_create(
                key=key,
                defaults={
                    "value": setting_data["value"],
                    "value_type": setting_data.get("value_type", "text"),
                    "description": setting_data["description"],
                    "is_sensitive": setting_data["is_sensitive"],
                    "requires_restart": setting_data["requires_restart"],
                    "updated_by": system_user
                }
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{key}" setting'))
            else:
                self.stdout.write(self.style.WARNING(f'"{key}" setting already exists'))

    def _create_business_hours(self, system_user):
        """Create default business hours"""
        business_hours_key = 'COMPANY_BUSINESS_HOURS'
        
        try:
            existing_hours = SystemSettings.objects.get(key=business_hours_key)
            self.stdout.write(self.style.WARNING('Business hours setting already exists'))
        except SystemSettings.DoesNotExist:
            default_business_hours = {
                "sameAsBusinessHours": False,
                "workShift": [
                    {"day": "Sunday", "active": False, "times": []},
                    {"day": "Monday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                    {"day": "Tuesday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                    {"day": "Wednesday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                    {"day": "Thursday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                    {"day": "Friday", "active": True, "times": [{"start": "09:00", "end": "18:00"}]},
                    {"day": "Saturday", "active": False, "times": []},
                ]
            }
            
            try:
                SystemSettings.objects.create(
                    key=business_hours_key,
                    value=json.dumps(default_business_hours),
                    value_type='json',
                    description='Company business hours',
                    updated_by=system_user
                )
                self.stdout.write(self.style.SUCCESS('Successfully created default business hours'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error creating business hours: {str(e)}'))

    # method to create 24/7 schedule for system user (Chatbot)
    def _create_system_user_schedule(self, system_user):
        """Create 24/7 work schedule for system user (chatbot)"""
        try:
            # Check if system user already has a schedule
            existing_schedule = UserSchedule.objects.filter(user=system_user).first()
            
            if existing_schedule:
                # Update existing schedule to 24/7
                existing_schedule.same_as_business_hours = False
                existing_schedule.schedule = {
                    "sameAsBusinessHours": False,
                    "workShift": [
                        {"day": "Sunday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Monday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Tuesday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Wednesday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Thursday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Friday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        {"day": "Saturday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                    ]
                }
                existing_schedule.save()
                self.stdout.write(self.style.SUCCESS('Updated system user schedule to 24/7 availability'))
            else:
                # Create new 24/7 schedule for system user
                UserSchedule.objects.create(
                    user=system_user,
                    same_as_business_hours=False,
                    schedule={
                        "sameAsBusinessHours": False,
                        "workShift": [
                            {"day": "Sunday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Monday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Tuesday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Wednesday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Thursday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Friday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                            {"day": "Saturday", "active": True, "times": [{"start": "00:00", "end": "23:59"}]},
                        ]
                    }
                )
                self.stdout.write(self.style.SUCCESS('Created 24/7 work schedule for system user (chatbot)'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating system user schedule: {str(e)}'))

    def _create_default_user_schedules(self):
        """Create default schedules for users without schedules"""
        try:
            from user.models import UserSchedule
            
            users_without_schedules = User.objects.filter(
                work_schedule__isnull=True
            ).exclude(username='system')
            schedules_created = 0
            
            for user in users_without_schedules:
                UserSchedule.objects.create(
                    user=user,
                    same_as_business_hours=True,
                    schedule={}
                )
                schedules_created += 1
            
            if schedules_created > 0:
                self.stdout.write(self.style.SUCCESS(f'Created default schedules for {schedules_created} users'))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'Could not create user schedules: {str(e)}'))

    def _create_default_channels(self, system_user):
        """Create default channels if environment variables are set"""
        # Create LINE channel if credentials exist
        if os.environ.get("LINE_CHANNEL_SECRET") and os.environ.get("LINE_ACCESS_TOKEN"):
            channel, created = LineChannel.objects.get_or_create(
                channel_id=os.environ.get("LINE_CHANNEL_ID", "default"),
                defaults={
                    'name': 'Default LINE Channel',
                    'channel_secret': os.environ.get("LINE_CHANNEL_SECRET"),
                    'channel_access_token': os.environ.get("LINE_ACCESS_TOKEN"),
                    'provider_id': 'P01',
                    'provider_name': 'Main Provider',
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS('Created default LINE channel'))
            else:
                self.stdout.write(self.style.WARNING('LINE channel already exists'))

    def _create_line_csat_score(self, system_user):
        """
        Create a CSAT score message for LINE using imagemap.
        This function prepares the CSAT survey message and returns it.
        Args:
            ticket_id (int): The ID of the ticket for which the CSAT score is being created.
        Returns:
        """

        # message_type: {
        #     "text": null,
        #     "quick_reply" : null,
        #     "image_map" : null,
        #     "image_carousel": {
        #         "line": {},
        #         "facebook" : null
        #     },
        #     "carousel" : null,
        #     "confirm_template": null,
        #     "buttons_template": null
        # }

        # message_template: {
        #     sentence: null,
        #     label: null,
        #     parent: null,
        #     message_type: {
        #         "text": null,
        #         "quick_reply" : null,
        #         "image_map" : null,
        #         "image_carousel": {
        #             "line": {},
        #             "facebook" : null
        #         },
        #         "carousel" : null,
        #         "confirm_template": null,
        #         "buttons_template": null
        #     }
        # }

        # TODO - Check this
        # TODO - Upload CSAT image to blob storage and get the URL

        # Process CSAT settings
        setting_key = "LINE_CSAT"
        setting = SystemSettings.objects.get(key=setting_key)
        setting_image_url = setting.value

        if setting.value_type == 'image':
            updated_key, updated_value = refresh_image_sas_tokens(setting)

        # Define CSAT imagemap message
        imagemap_required_string = "&w=auto"
        base_url = setting_image_url + imagemap_required_string
        alt_text = "LINE CSAT imagemap"
        label="CSAT_Rating"
        label = label.strip()
        action = label.strip().replace(" ", "_")

        messages = ImagemapMessage(
                type="imagemap",
                base_url=base_url,
                alt_text=alt_text,
                base_size=ImagemapBaseSize(
                    width=1040,
                    height=1040
                ),
                actions=[],
                quickReply=QuickReply(
                    items=[
                        QuickReplyItem(
                            action=PostbackAction(
                                label="ปรับปรุง (1/5)",
                                display_text="ปรับปรุง",
                                # data=f"action=csat&rating=1&text=ปรับปรุง&ticket_id={ticket_id}"
                                data=f"action={action}csat&variable=csat_rating&value=1"
                            )
                        ),
                        QuickReplyItem(
                            action=PostbackAction(
                                label="แย่ (2/5)",
                                display_text="แย่",
                                # data=f"action=csat&rating=2&text=แย่&ticket_id={ticket_id}"
                                data=f"action={action}&variable=csat_rating&value=2"
                            )
                        ),
                        QuickReplyItem(
                            action=PostbackAction(
                                label="เฉย (3/5)",
                                display_text="เฉย",
                                # data=f"action=csat&rating=3&text=เฉย&ticket_id={ticket_id}"
                                data=f"action={action}&variable=csat_rating&value=3"
                            )
                        ),
                        QuickReplyItem(
                            action=PostbackAction(
                                label="ดี (4/5)",
                                display_text="ดี",
                                # data=f"action=csat&rating=4&text=ดี&ticket_id={ticket_id}"
                                data=f"action={action}&variable=csat_rating&value=4"
                            )
                        ),
                        QuickReplyItem(
                            action=PostbackAction(
                                label="ดีมาก (5/5)",
                                display_text="ดีมาก",
                                # data=f"action=csat&rating=5&text=ดีมาก&ticket_id={ticket_id}"
                                data=f"action={action}&variable=csat_rating&value=5"
                            )
                        ),
                    ]
                )
            )
        message_dict = {
            'message_type': {
                'image_map': {
                    'line': messages.to_dict(), # Add social platform for this object
                    # 'line': messages.to_json(), # Add social platform for this object
                }
            }
        }

        # Create MessageTemplate instance
        csat_message_template, created = MessageTemplate.objects.get_or_create(
            sentence=["Please rate our service"],
            label="CSAT Survey",
            parent=None,
            defaults={
                'status': 'default',
                'department': [],
                'message_type_image_map': message_dict['message_type']['image_map'],
                'created_by': system_user
            }
        )
        if created:
            self.stdout.write("CSAT message template created successfully.")
        else:
            self.stdout.write("CSAT message template already exists.")

    # Add this method to your Command class
    def _create_consent_agreements(self, system_user):
        """Initialize PDPA and other consent agreements"""
        self.stdout.write(self.style.NOTICE('Creating consent agreements...'))
        
        # 1. Create Agreement Types
        agreement_types = [
            {
                'code': 'PDPA',
                'name': 'Personal Data Protection Act',
                'description': 'ข้อตกลงการคุ้มครองข้อมูลส่วนบุคคลตามพระราชบัญญัติคุ้มครองข้อมูลส่วนบุคคล',
                'is_mandatory': True,
                'scope_level': 'CUSTOMER',
                'display_order': 1
            },
            {
                'code': 'TOS',
                'name': 'Terms of Service',
                'description': 'ข้อกำหนดและเงื่อนไขการใช้บริการ',
                'is_mandatory': True,
                'scope_level': 'CUSTOMER',
                'display_order': 2
            },
            {
                'code': 'MARKETING',
                'name': 'Marketing Communications',
                'description': 'การติดต่อสื่อสารเพื่อการตลาดและประชาสัมพันธ์',
                'is_mandatory': False,
                'scope_level': 'PLATFORM_IDENTITY',
                'display_order': 3
            }
        ]
        
        created_types = {}
        for type_data in agreement_types:
            agreement_type, created = AgreementType.objects.get_or_create(
                code=type_data['code'],
                defaults={
                    **type_data,
                    'created_by': system_user
                }
            )
            created_types[type_data['code']] = agreement_type
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created "{agreement_type.name}" agreement type'))
            else:
                self.stdout.write(self.style.WARNING(f'"{agreement_type.name}" agreement type already exists'))
        
        # 2. Create Agreement Versions
        self._create_pdpa_versions(created_types['PDPA'], system_user)
        self._create_tos_versions(created_types['TOS'], system_user)
        self._create_marketing_versions(created_types['MARKETING'], system_user)
        
        # 3. Create Consent Purposes
        self._create_consent_purposes(created_types, system_user)

    def _create_pdpa_versions(self, pdpa_type, system_user):
        """Create PDPA agreement versions in Thai and English"""
        versions = [
            {
                'version': '1.0',
                'language_code': 'th',
                'title': 'นโยบายความเป็นส่วนตัว และข้อตกลงการประมวลผลข้อมูลส่วนบุคคล',
                'content': '''
    <h3>นโยบายความเป็นส่วนตัว และข้อตกลงการประมวลผลข้อมูลส่วนบุคคล</h3>

    <p>บริษัทของเราให้ความสำคัญกับการคุ้มครองข้อมูลส่วนบุคคลของท่าน ตามพระราชบัญญัติคุ้มครองข้อมูลส่วนบุคคล พ.ศ. 2562</p>

    <h4>1. ข้อมูลที่เราเก็บรวบรวม</h4>
    <ul>
        <li>ข้อมูลส่วนตัว: ชื่อ นามสกุล เลขบัตรประชาชน วันเกิด</li>
        <li>ข้อมูลการติดต่อ: เบอร์โทรศัพท์ อีเมล ที่อยู่</li>
        <li>ข้อมูลการใช้บริการ: ประวัติการทำธุรกรรม การใช้งานแพลตฟอร์ม</li>
    </ul>

    <h4>2. วัตถุประสงค์ในการเก็บรวบรวมข้อมูล</h4>
    <ul>
        <li>เพื่อให้บริการตามที่ท่านร้องขอ</li>
        <li>เพื่อยืนยันตัวตนและป้องกันการทุจริต</li>
        <li>เพื่อปฏิบัติตามกฎหมายที่เกี่ยวข้อง</li>
        <li>เพื่อวิเคราะห์และปรับปรุงบริการ (หากท่านยินยอม)</li>
    </ul>

    <h4>3. การเปิดเผยข้อมูล</h4>
    <p>เราจะไม่เปิดเผยข้อมูลส่วนบุคคลของท่านให้กับบุคคลที่สาม เว้นแต่:</p>
    <ul>
        <li>ได้รับความยินยอมจากท่าน</li>
        <li>เป็นการปฏิบัติตามกฎหมาย</li>
        <li>เพื่อปกป้องสิทธิและทรัพย์สินของบริษัท</li>
    </ul>

    <h4>4. สิทธิของเจ้าของข้อมูล</h4>
    <p>ท่านมีสิทธิตาม พ.ร.บ. คุ้มครองข้อมูลส่วนบุคคล ดังนี้:</p>
    <ul>
        <li>สิทธิในการเข้าถึงข้อมูลส่วนบุคคล</li>
        <li>สิทธิในการแก้ไขข้อมูลส่วนบุคคล</li>
        <li>สิทธิในการลบข้อมูลส่วนบุคคล</li>
        <li>สิทธิในการคัดค้านการประมวลผลข้อมูล</li>
        <li>สิทธิในการเพิกถอนความยินยอม</li>
    </ul>

    <h4>5. การติดต่อ</h4>
    <p>หากท่านมีข้อสงสัยเกี่ยวกับนโยบายนี้ กรุณาติดต่อเจ้าหน้าที่คุ้มครองข้อมูลส่วนบุคคล:</p>
    <p>อีเมล: <EMAIL><br>โทร: 02-XXX-XXXX</p>
                '''
            },
            {
                'version': '1.0',
                'language_code': 'en',
                'title': 'Privacy Policy and Personal Data Processing Agreement',
                'content': '''
    <h3>Privacy Policy and Personal Data Processing Agreement</h3>

    <p>We value the protection of your personal data in accordance with the Personal Data Protection Act B.E. 2562 (2019)</p>

    <h4>1. Data We Collect</h4>
    <ul>
        <li>Personal Information: Name, National ID, Date of Birth</li>
        <li>Contact Information: Phone Number, Email, Address</li>
        <li>Service Usage: Transaction History, Platform Usage</li>
    </ul>

    <h4>2. Purpose of Data Collection</h4>
    <ul>
        <li>To provide services as requested</li>
        <li>To verify identity and prevent fraud</li>
        <li>To comply with relevant laws</li>
        <li>To analyze and improve services (with your consent)</li>
    </ul>

    <h4>3. Data Disclosure</h4>
    <p>We will not disclose your personal data to third parties except:</p>
    <ul>
        <li>With your consent</li>
        <li>To comply with legal obligations</li>
        <li>To protect our rights and property</li>
    </ul>

    <h4>4. Data Subject Rights</h4>
    <p>You have the following rights under PDPA:</p>
    <ul>
        <li>Right to access personal data</li>
        <li>Right to rectify personal data</li>
        <li>Right to delete personal data</li>
        <li>Right to object to data processing</li>
        <li>Right to withdraw consent</li>
    </ul>

    <h4>5. Contact Us</h4>
    <p>For questions about this policy, please contact our Data Protection Officer:</p>
    <p>Email: <EMAIL><br>Tel: 02-XXX-XXXX</p>
                '''
            }
        ]
        
        for version_data in versions:
            version, created = AgreementVersion.objects.get_or_create(
                agreement_type=pdpa_type,
                version=version_data['version'],
                language_code=version_data['language_code'],
                defaults={
                    'title': version_data['title'],
                    'content': version_data['content'],
                    'effective_date': timezone.now(),
                    'is_active': True,
                    'requires_re_consent': False,
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(
                    f'Successfully created PDPA {version_data["language_code"].upper()} version {version.version}'
                ))
            else:
                self.stdout.write(self.style.WARNING(
                    f'PDPA {version_data["language_code"].upper()} version {version.version} already exists'
                ))

    def _create_tos_versions(self, tos_type, system_user):
        """Create Terms of Service versions"""
        versions = [
            {
                'version': '1.0',
                'language_code': 'th',
                'title': 'ข้อกำหนดและเงื่อนไขการใช้บริการ',
                'content': '''
    <h3>ข้อกำหนดและเงื่อนไขการใช้บริการ</h3>

    <p>ข้อกำหนดและเงื่อนไขนี้มีผลบังคับใช้เมื่อท่านใช้บริการของเรา</p>

    <h4>1. การยอมรับข้อกำหนด</h4>
    <p>การใช้บริการของเราถือว่าท่านยอมรับข้อกำหนดนี้</p>

    <h4>2. การใช้บริการ</h4>
    <ul>
        <li>ท่านต้องมีอายุ 20 ปีขึ้นไป</li>
        <li>ท่านต้องให้ข้อมูลที่ถูกต้องและเป็นปัจจุบัน</li>
        <li>ท่านต้องรักษาความปลอดภัยของบัญชีของท่าน</li>
    </ul>

    <h4>3. ข้อห้าม</h4>
    <ul>
        <li>ห้ามใช้บริการเพื่อวัตถุประสงค์ที่ผิดกฎหมาย</li>
        <li>ห้ามแอบอ้างหรือปลอมแปลงตัวตน</li>
        <li>ห้ามรบกวนหรือทำลายระบบ</li>
    </ul>
                '''
            },
            {
                'version': '1.0',
                'language_code': 'en',
                'title': 'Terms of Service',
                'content': '''
    <h3>Terms of Service</h3>

    <p>These terms apply when you use our services.</p>

    <h4>1. Acceptance of Terms</h4>
    <p>By using our services, you accept these terms.</p>

    <h4>2. Use of Services</h4>
    <ul>
        <li>You must be at least 20 years old</li>
        <li>You must provide accurate and current information</li>
        <li>You must maintain the security of your account</li>
    </ul>

    <h4>3. Prohibited Activities</h4>
    <ul>
        <li>Using services for illegal purposes</li>
        <li>Impersonating others</li>
        <li>Disrupting or damaging the system</li>
    </ul>
                '''
            }
        ]
        
        for version_data in versions:
            version, created = AgreementVersion.objects.get_or_create(
                agreement_type=tos_type,
                version=version_data['version'],
                language_code=version_data['language_code'],
                defaults={
                    'title': version_data['title'],
                    'content': version_data['content'],
                    'effective_date': timezone.now(),
                    'is_active': True,
                    'requires_re_consent': False,
                    'created_by': system_user
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(
                    f'Successfully created TOS {version_data["language_code"].upper()} version {version.version}'
                ))

    def _create_marketing_versions(self, marketing_type, system_user):
        """Create Marketing agreement versions"""
        version_data = {
            'version': '1.0',
            'language_code': 'th',
            'title': 'ความยินยอมรับข้อมูลข่าวสารและการตลาด',
            'content': '''
    <h3>ความยินยอมรับข้อมูลข่าวสารและการตลาด</h3>

    <p>เราอยากติดต่อสื่อสารกับท่านเพื่อแจ้งข่าวสาร โปรโมชั่น และข้อเสนอพิเศษต่างๆ</p>

    <h4>ช่องทางการติดต่อ</h4>
    <p>ท่านสามารถเลือกช่องทางที่ต้องการรับข่าวสาร:</p>
    <ul>
        <li>อีเมล - รับจดหมายข่าวและโปรโมชั่นทางอีเมล</li>
        <li>SMS - รับข้อความสั้นแจ้งข้อเสนอพิเศษ</li>
        <li>LINE - รับการแจ้งเตือนผ่าน LINE Official Account</li>
        <li>โฆษณาส่วนบุคคล - แสดงโฆษณาที่เหมาะกับความสนใจของท่าน</li>
    </ul>

    <h4>การยกเลิก</h4>
    <p>ท่านสามารถยกเลิกการรับข่าวสารได้ทุกเมื่อผ่านการตั้งค่าในบัญชีของท่าน</p>
            '''
        }
        
        version, created = AgreementVersion.objects.get_or_create(
            agreement_type=marketing_type,
            version=version_data['version'],
            language_code=version_data['language_code'],
            defaults={
                'title': version_data['title'],
                'content': version_data['content'],
                'effective_date': timezone.now(),
                'is_active': True,
                'requires_re_consent': False,
                'created_by': system_user
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS(
                f'Successfully created Marketing {version_data["language_code"].upper()} version {version.version}'
            ))

    def _create_consent_purposes(self, agreement_types, system_user):
        """Create consent purposes for each agreement type"""
        
        # PDPA Purposes
        pdpa_purposes = [
            {
                'code': 'BASIC_PROCESSING',
                'name': 'การประมวลผลข้อมูลพื้นฐาน',
                'description': 'ประมวลผลข้อมูลส่วนบุคคลเพื่อให้บริการตามที่ท่านร้องขอ',
                'is_mandatory': True,
                'display_order': 1
            },
            {
                'code': 'ANALYTICS',
                'name': 'การวิเคราะห์และปรับปรุงบริการ',
                'description': 'ใช้ข้อมูลเพื่อวิเคราะห์และปรับปรุงคุณภาพการให้บริการ',
                'is_mandatory': False,
                'display_order': 2
            },
            {
                'code': 'SECURITY',
                'name': 'ความปลอดภัยและป้องกันการทุจริต',
                'description': 'ใช้ข้อมูลเพื่อรักษาความปลอดภัยและป้องกันการทุจริต',
                'is_mandatory': True,
                'display_order': 3
            }
        ]
        
        pdpa_type = agreement_types['PDPA']
        for purpose_data in pdpa_purposes:
            purpose, created = ConsentPurpose.objects.get_or_create(
                agreement_type=pdpa_type,
                code=purpose_data['code'],
                defaults={
                    **purpose_data,
                    'created_on': timezone.now(),
                    'updated_on': timezone.now()
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created PDPA purpose: {purpose.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'PDPA purpose already exists: {purpose.name}'))
        
        # Marketing Purposes
        marketing_purposes = [
            {
                'code': 'EMAIL_MARKETING',
                'name': 'การตลาดทางอีเมล',
                'description': 'รับจดหมายข่าว โปรโมชั่น และข้อเสนอพิเศษทางอีเมล',
                'is_mandatory': False,
                'display_order': 1
            },
            {
                'code': 'SMS_MARKETING',
                'name': 'การตลาดทาง SMS',
                'description': 'รับข้อความสั้นแจ้งโปรโมชั่นและข้อเสนอพิเศษ',
                'is_mandatory': False,
                'display_order': 2
            },
            {
                'code': 'LINE_NOTIFICATIONS',
                'name': 'การแจ้งเตือนผ่าน LINE',
                'description': 'รับการแจ้งเตือนและข่าวสารผ่าน LINE Official Account',
                'is_mandatory': False,
                'display_order': 3
            },
            {
                'code': 'PERSONALIZED_ADS',
                'name': 'โฆษณาส่วนบุคคล',
                'description': 'แสดงโฆษณาและเนื้อหาที่ปรับให้เหมาะกับความสนใจของท่าน',
                'is_mandatory': False,
                'display_order': 4
            }
        ]
        
        marketing_type = agreement_types['MARKETING']
        for purpose_data in marketing_purposes:
            purpose, created = ConsentPurpose.objects.get_or_create(
                agreement_type=marketing_type,
                code=purpose_data['code'],
                defaults={
                    **purpose_data,
                    'created_on': timezone.now(),
                    'updated_on': timezone.now()
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created Marketing purpose: {purpose.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Marketing purpose already exists: {purpose.name}'))
                
                
    def _create_slas(self):
        slas = [
            {"name": "response_time", "channel": "phone", "value": "6", "unit": "second", "category": "general"},
            {"name": "response_time", "channel": "facebook", "value": "7", "unit": "minute", "category": "general"},
            {"name": "response_time", "channel": "line", "value": "7", "unit": "minute", "category": "general"},
            {"name": "handling_time", "channel": "phone", "value": "1", "unit": "hour", "category": "general"},
            {"name": "handling_time", "channel": "facebook", "value": "5", "unit": "minute", "category": "general"},
            {"name": "handling_time", "channel": "line", "value": "1", "unit": "hour", "category": "general"},
            {"name": "csat", "channel": "phone", "value": "4.5", "unit": "score", "category": "general"},
            {"name": "csat", "channel": "facebook", "value": "4.0", "unit": "score", "category": "general"},
            {"name": "csat", "channel": "line", "value": "4.0", "unit": "score", "category": "general"},
            {"name": "answered_call", "channel": "phone", "value": "95", "unit": "percentage", "category": "general"},
            {"name": "report_generation_time", "channel": "system", "value": "17:00", "unit": "time", "category": "general"},
            {"name": "report_frequency", "channel": "system", "value": "daily", "unit": "frequency", "category": "general"},
        ]

        for sla_data in slas:
            sla, created = SLA.objects.get_or_create(
                name=sla_data["name"],
                channel=sla_data["channel"],
                defaults={
                    "value": sla_data["value"],
                    "unit": sla_data["unit"],
                    "category": sla_data["category"],  
                }
            )
            if created:
                self.stdout.write(self.style.SUCCESS(f"Created SLA: {sla}"))
            else:
                # Update value, unit, and category if already exists
                sla.value = sla_data["value"]
                sla.unit = sla_data["unit"]
                sla.category = sla_data["category"]
                sla.save(update_fields=["value", "unit", "category"])
                self.stdout.write(self.style.WARNING(f"Updated SLA: {sla}"))

        self.stdout.write(self.style.SUCCESS("SLA initialization complete."))
